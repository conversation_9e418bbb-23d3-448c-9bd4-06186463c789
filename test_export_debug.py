#!/usr/bin/env python3
"""
Debug script to test the export functionality without running the full Flask app
"""

import sys
import os
import logging

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

# Set up logging
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_export_functionality():
    """Test the export functionality with a known report ID"""

    try:
        # Import the custom report generator
        from utils.custom_report_generator import CustomReportGenerator

        # Test with the existing report ID
        report_id = "testsuite_execution_20250627_181306"
        app_root_path = os.path.join(os.path.dirname(__file__), 'app')

        logger.info(f"Testing export for report ID: {report_id}")
        logger.info(f"App root path: {app_root_path}")

        # Create the generator directly to get more detailed error info
        generator = CustomReportGenerator(report_id, app_root_path)

        # Test each step individually
        print("  📁 Testing directory setup...")
        logger.info(f"Source report dir: {generator.source_report_dir}")
        logger.info(f"Template path: {generator.template_path}")

        print("  📄 Testing data loading...")
        test_data = generator._load_test_data()
        logger.info(f"Loaded test data keys: {list(test_data.keys()) if test_data else 'None'}")

        if not test_data:
            print("❌ No test data loaded!")
            return False

        print("  🎨 Testing report generation...")
        report_success = generator.generate_report()
        logger.info(f"Report generation success: {report_success}")

        if not report_success:
            print("❌ Report generation failed!")
            return False

        print("  📦 Testing ZIP creation...")
        zip_path = generator.create_zip()
        logger.info(f"ZIP creation result: {zip_path}")

        if zip_path:
            print(f"✅ Export successful: {zip_path}")
            return True
        else:
            print("❌ ZIP creation failed!")
            return False

    except Exception as e:
        logger.exception(f"Error during export test: {e}")
        print(f"❌ Exception during export: {e}")
        return False

def test_template_exists():
    """Test if the template file exists"""
    template_path = os.path.join(os.path.dirname(__file__), 'app', 'templates', 'custom_report_template.html')
    exists = os.path.exists(template_path)
    logger.info(f"Template exists at {template_path}: {exists}")
    return exists

def test_reports_directory():
    """Test if the reports directory is accessible"""
    reports_dir = "/Users/<USER>/Documents/automation-tool/reports"
    exists = os.path.exists(reports_dir)
    logger.info(f"Reports directory exists at {reports_dir}: {exists}")
    
    if exists:
        # List contents
        contents = os.listdir(reports_dir)
        logger.info(f"Reports directory contents: {contents}")
    
    return exists

if __name__ == "__main__":
    print("🔍 Testing Export Functionality")
    print("=" * 50)
    
    # Test prerequisites
    print("\n1. Testing template file...")
    template_ok = test_template_exists()
    
    print("\n2. Testing reports directory...")
    reports_ok = test_reports_directory()
    
    print("\n3. Testing export functionality...")
    if template_ok and reports_ok:
        export_ok = test_export_functionality()
    else:
        print("❌ Prerequisites not met, skipping export test")
        export_ok = False
    
    print("\n" + "=" * 50)
    if export_ok:
        print("✅ All tests passed!")
    else:
        print("❌ Some tests failed!")
